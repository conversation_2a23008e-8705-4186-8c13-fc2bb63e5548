import * as dotenv from 'dotenv';

dotenv.config();

const CHAT_SEARCH_SIZE: string = process.env['CHAT_SEARCH_SIZE'] ?? '50';
const CHAT_START_DATE: string = process.env['CHAT_START_DATE'] ?? '2025-01-31';
const CHAT_END_DATE: string = process.env['CHAT_END_DATE'] ?? '2025-12-31';

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

export function createUserChatRequests(userId: string): TeamsChannelRequest[] {
  if (!userId) return [];

  return [{
    id: userId,
    method: 'GET',
    url: `/users/${userId}/chats?$top=${CHAT_SEARCH_SIZE}&$filter=lastUpdatedDateTime ge ${CHAT_START_DATE}T00:00:00Z and lastUpdatedDateTime le ${CHAT_END_DATE}T23:59:59Z`
  }];
}

export function createUserUniqueChatMessagesRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/messages?$top=${CHAT_SEARCH_SIZE}&$filter=lastModifiedDateTime gt ${CHAT_START_DATE}T00:00:00Z and lastModifiedDateTime lt ${CHAT_END_DATE}T23:59:59Z`
  }];
}

export function createChatsMembersRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  return [{
    id: chatId,
    method: 'GET',
    url: `/chats/${chatId}/members`
  }];
}